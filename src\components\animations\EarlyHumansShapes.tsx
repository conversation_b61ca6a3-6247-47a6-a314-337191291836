import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface EarlyHumansShapesProps {
  progress: number; // This will be used to time the fire ignition
}

// A single twinkling star
const Star: React.FC<{ cx: string; cy: string; size: number }> = ({ cx, cy, size }) => {
  return (
    <motion.circle
      cx={cx}
      cy={cy}
      r={size}
      fill="rgba(255, 255, 255, 0.8)"
      animate={{
        opacity: [0.6, 1, 0.6],
      }}
      transition={{
        duration: Math.random() * 2 + 2,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    />
  );
};

// A simple constellation
const Constellation: React.FC<{ stars: { cx: string; cy: string; size: number }[] }> = ({ stars }) => {
  return (
    <g>
      {stars.map((star, i) => (
        <Star key={i} {...star} />
      ))}
    </g>
  );
};

// The fire particle system
const FireParticles: React.FC = () => {
  const particles = useMemo(() => {
    return Array.from({ length: 50 }).map((_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: 100 + Math.random() * 10,
      size: Math.random() * 1.5 + 0.5,
      duration: Math.random() * 3 + 2,
      delay: Math.random() * 2,
      color: ['#ffbe0b', '#fb5607', '#ff006e', '#8338ec'].at(Math.floor(Math.random() * 4)),
    }));
  }, []);

  return (
    <svg className="absolute inset-0 w-full h-full z-30" preserveAspectRatio="none">
      {particles.map(({ id, x, y, size, duration, delay, color }) => (
        <motion.circle
          key={id}
          cx={`${x}%`}
          cy={`${y}%`}
          r={size}
          fill={color}
          initial={{ y: `${y}%`, opacity: 0 }}
          animate={{
            y: '-10%',
            opacity: [0, 0.8, 0],
          }}
          transition={{
            duration,
            delay,
            repeat: Infinity,
            ease: 'linear',
          }}
        />
      ))}
    </svg>
  );
};

const EarlyHumansShapes: React.FC<EarlyHumansShapesProps> = ({ progress }) => {
  // Fire should start after the first stage (17s / 50s total duration for the era)
  const fireIgnitionProgress = 17 / 50;
  const showFire = progress > fireIgnitionProgress;

  const constellations = useMemo(() => [
    { stars: [{ cx: '20%', cy: '15%', size: 0.3 }, { cx: '25%', cy: '12%', size: 0.4 }, { cx: '28%', cy: '18%', size: 0.2 }] },
    { stars: [{ cx: '70%', cy: '20%', size: 0.3 }, { cx: '75%', cy: '25%', size: 0.2 }, { cx: '80%', cy: '22%', size: 0.4 }] },
    { stars: [{ cx: '50%', cy: '30%', size: 0.2 }, { cx: '48%', cy: '35%', size: 0.3 }] },
  ], []);

  return (
    <div className="absolute inset-0" data-testid="early-humans-container">
      {/* Dark Sky Gradient */}
      <div
        className="absolute top-0 left-0 w-full h-1/2 z-10"
        style={{
          background: 'linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)',
        }}
      />

      {/* Constellations */}
      <svg className="absolute inset-0 w-full h-full overflow-visible z-20">
        {constellations.map((c, i) => <Constellation key={i} stars={c.stars} />)}
      </svg>

      {/* Fire Animation */}
      {showFire && <FireParticles />}
    </div>
  );
};

export default EarlyHumansShapes;
