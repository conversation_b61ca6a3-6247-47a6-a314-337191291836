import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface EarlyHumansShapesProps {
  progress: number; // This will be used to time the fire ignition
}

// A single twinkling star
const Star: React.FC<{ cx: string; cy: string; size: number }> = ({ cx, cy, size }) => {
  return (
    <motion.circle
      cx={cx}
      cy={cy}
      r={size}
      fill="rgba(255, 255, 255, 0.8)"
      animate={{
        opacity: [0.6, 1, 0.6],
      }}
      transition={{
        duration: Math.random() * 2 + 2,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    />
  );
};

// A simple constellation
const Constellation: React.FC<{ stars: { cx: string; cy: string; size: number }[] }> = ({ stars }) => {
  return (
    <g>
      {stars.map((star, i) => (
        <Star key={i} {...star} />
      ))}
    </g>
  );
};

// The fire particle system
const FireParticles: React.FC = () => {
  const particles = useMemo(() => {
    const fireColors = ['#ffbe0b', '#fb5607', '#ff4500', '#ff6b35', '#ffa500'];
    return Array.from({ length: 80 }).map((_, i) => { // Increased particle count for full-screen effect
      const color = fireColors[Math.floor(Math.random() * fireColors.length)];
      return {
        id: i,
        x: Math.random() * 100, // Spread across the entire screen width
        y: 100 + Math.random() * 10, // Start from the bottom
        size: Math.random() * 2 + 1, // Slightly smaller particles for a more ember-like feel
        duration: Math.random() * 5 + 4, // Slower, more ambient rise (4-9s)
        delay: Math.random() * 5, // Stagger over 5 seconds
        color,
      };
    });
  }, []);

  return (
    <svg className="absolute inset-0 w-full h-full z-30" preserveAspectRatio="none">
      {particles.map(({ id, x, y, size, duration, delay, color }) => (
        <motion.circle
          key={id}
          cx={`${x}%`}
          cy={`${y}%`}
          r={size}
          fill={color}
          initial={{ y: `${y}%`, opacity: 0 }}
          animate={{
            y: '-10%', // Rise all the way to the top and off-screen
            opacity: [0, 0.7, 0.7, 0],
            scale: [0.8, 1, 0.8],
          }}
          transition={{
            duration,
            delay,
            repeat: Infinity,
            ease: 'linear',
          }}
          style={{
            // Create a glowing effect using a drop-shadow filter
            filter: `blur(1px) drop-shadow(0 0 4px ${color}) drop-shadow(0 0 8px ${color})`,
          }}
        />
      ))}
    </svg>
  );
};

const EarlyHumansShapes: React.FC<EarlyHumansShapesProps> = ({ progress }) => {
  // Fire should start after the first stage (18s / 50s total duration for the era)
  // First stage duration is 18 seconds out of 50 total seconds
  const fireIgnitionProgress = 18 / 50; // 0.36 (36%)
  const showFire = progress > fireIgnitionProgress;



  const constellations = useMemo(() => [
    { stars: [{ cx: '20%', cy: '15%', size: 0.3 }, { cx: '25%', cy: '12%', size: 0.4 }, { cx: '28%', cy: '18%', size: 0.2 }] },
    { stars: [{ cx: '70%', cy: '20%', size: 0.3 }, { cx: '75%', cy: '25%', size: 0.2 }, { cx: '80%', cy: '22%', size: 0.4 }] },
    { stars: [{ cx: '50%', cy: '30%', size: 0.2 }, { cx: '48%', cy: '35%', size: 0.3 }] },
  ], []);

  return (
    <div className="absolute inset-0" data-testid="early-humans-container">
      {/* Dark Sky Gradient */}
      <div
        className="absolute top-0 left-0 w-full h-1/2 z-10"
        style={{
          background: 'linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%)',
        }}
      />

      {/* Constellations */}
      <svg className="absolute inset-0 w-full h-full overflow-visible z-20">
        {constellations.map((c, i) => <Constellation key={i} stars={c.stars} />)}
      </svg>

      {/* Fire Animation */}
      {showFire && (
        <>
          {/* Fire Base Glow */}
          <div
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 z-25"
            style={{
              width: '200px',
              height: '100px',
              background: 'radial-gradient(ellipse at center bottom, rgba(255, 100, 0, 0.3) 0%, rgba(255, 150, 0, 0.2) 40%, transparent 70%)',
              filter: 'blur(10px)',
            }}
          />
          <FireParticles />
        </>
      )}

      {/* Debug Info (for testing) */}
      <div
        className="hidden"
        data-testid="fire-debug"
        data-progress={progress.toFixed(3)}
        data-show-fire={showFire}
      />
    </div>
  );
};

export default EarlyHumansShapes;
