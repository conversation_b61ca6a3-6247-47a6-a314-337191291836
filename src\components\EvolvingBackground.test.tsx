import React from 'react';
import { render, screen, act } from '@testing-library/react';
import EvolvingBackground from '@/components/EvolvingBackground';
import { Era } from '@/types';
import { useAnimation } from 'framer-motion';

// Mock framer-motion's useAnimation hook
const mockStart = jest.fn();
jest.mock('framer-motion', () => ({
  ...jest.requireActual('framer-motion'),
  useAnimation: () => ({ start: mockStart }),
}));

// Mock the era visuals data
jest.mock('@/data/eraVisuals.json', () => ({
  'primordial-soup': {
    colors: ['#0a0a0a', '#1a1a1a', '#2a2a2a'],
    transitionDuration: 2,
  },
  'multi-stage-era': [
    {
      colors: ['#ff0000', '#00ff00', '#0000ff'],
      stageDuration: 5000, // 5s
      transitionDuration: 1,
    },
    {
      colors: ['#ffff00', '#ff00ff', '#00ffff'],
      transitionDuration: 1.5,
    },
  ],
}));

describe('EvolvingBackground', () => {
  // Corrected Era types: removed name, startTime, endTime
  const primordialSoupEra: Era = { id: 'primordial-soup' };
  const multiStageEra: Era = { id: 'multi-stage-era' };

  beforeEach(() => {
    jest.useFakeTimers();
    mockStart.mockClear();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders with initial styles and calls animation to hide when not playing', () => {
    render(<EvolvingBackground currentEra={primordialSoupEra} isPlaying={false} />);

    expect(screen.getByTestId('evolving-background')).toBeInTheDocument();

    // It should immediately animate to opacity 0
    expect(mockStart).toHaveBeenCalledWith({ opacity: 0, transition: { duration: 1 } });
  });

  it('calls animation to show with correct colors when playing', () => {
    render(<EvolvingBackground currentEra={primordialSoupEra} isPlaying={true} />);

    expect(mockStart).toHaveBeenCalledWith({
      opacity: 1,
      '--gradient-color-1': '#0a0a0a',
      '--gradient-color-2': '#1a1a1a',
      '--gradient-color-3': '#2a2a2a',
      transition: { duration: 2, ease: 'easeInOut' },
    });
  });

  it('transitions through stages for multi-stage eras by calling animate', () => {
    const { rerender } = render(<EvolvingBackground currentEra={multiStageEra} isPlaying={true} />);

    // Initial stage animation
    expect(mockStart).toHaveBeenCalledWith({
      opacity: 1,
      '--gradient-color-1': '#ff0000',
      '--gradient-color-2': '#00ff00',
      '--gradient-color-3': '#0000ff',
      transition: { duration: 1, ease: 'easeInOut' },
    });

    mockStart.mockClear();

    // Advance time to trigger the next stage
    act(() => {
      jest.advanceTimersByTime(5000);
    });

    // Rerender to process state update from timeout
    rerender(<EvolvingBackground currentEra={multiStageEra} isPlaying={true} />);

    // Second stage animation
    expect(mockStart).toHaveBeenCalledWith({
      opacity: 1,
      '--gradient-color-1': '#ffff00',
      '--gradient-color-2': '#ff00ff',
      '--gradient-color-3': '#00ffff',
      transition: { duration: 1.5, ease: 'easeInOut' },
    });
  });

  it('resets stage index and animations when the era changes', () => {
    const { rerender } = render(<EvolvingBackground currentEra={multiStageEra} isPlaying={true} />);

    // Initial stage animation for multi-stage era
    expect(mockStart).toHaveBeenCalledTimes(1);
    expect(mockStart).toHaveBeenCalledWith(expect.objectContaining({ '--gradient-color-1': '#ff0000' }));

    // Advance time
    act(() => {
      jest.advanceTimersByTime(5000);
    });
    rerender(<EvolvingBackground currentEra={multiStageEra} isPlaying={true} />);

    // Second stage animation
    expect(mockStart).toHaveBeenCalledTimes(2);
    expect(mockStart).toHaveBeenCalledWith(expect.objectContaining({ '--gradient-color-1': '#ffff00' }));

    // Change era
    rerender(<EvolvingBackground currentEra={primordialSoupEra} isPlaying={true} />);

    // Animation for the new era
    expect(mockStart).toHaveBeenCalledTimes(3);
    expect(mockStart).toHaveBeenCalledWith(expect.objectContaining({ '--gradient-color-1': '#0a0a0a' }));

    // Change back to multi-stage era, should be at stage 0
    rerender(<EvolvingBackground currentEra={multiStageEra} isPlaying={true} />);
    expect(mockStart).toHaveBeenCalledTimes(4);
    expect(mockStart).toHaveBeenCalledWith(expect.objectContaining({ '--gradient-color-1': '#ff0000' }));
  });
});
