'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface MammalDawnShapesProps {
  progress: number;
}

const MammalDawnShapes: React.FC<MammalDawnShapesProps> = ({ progress }) => {
  // DEBUG: Render a simple, static element to test visibility
  return (
    <div className="absolute inset-0 flex items-center justify-center" data-testid="mammal-dawn-container">
      <div
        style={{
          width: '200px',
          height: '200px',
          backgroundColor: 'red',
          opacity: 1, // Ensure it's fully visible
        }}
      />
    </div>
  );
};

export default MammalDawnShapes;
