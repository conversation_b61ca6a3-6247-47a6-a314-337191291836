'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface MammalDawnShapesProps {
  progress: number;
}

// Component for the background drifting particles
const DriftingParticles: React.FC = () => {
  const particleCount = 75;
  const particles = useMemo(() => {
    return Array.from({ length: particleCount }).map((_, i) => ({
      id: `particle-${i}`,
      startX: `${Math.random() * 100}%`,
      startY: `${Math.random() * 100}%`,
      size: Math.random() * 6 + 3,
      color: `hsla(${Math.random() * 60 + 80}, 100%, 75%, ${Math.random() * 0.4 + 0.3})`,
      duration: Math.random() * 20 + 15,
      delay: Math.random() * 20,
    }));
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: particle.startX,
            top: particle.startY,
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color,
            filter: 'blur(6px)',
          }}
          initial={{ y: '10vh', opacity: 0 }}
          animate={{ y: '-110vh', opacity: [0, 1, 1, 0] }}
          transition={{
            duration: particle.duration,
            delay: particle.delay,
            ease: 'linear',
            repeat: Infinity,
            repeatType: 'loop',
          }}
        />
      ))}
    </div>
  );
};

// Component for a single, procedurally generated branching tree with collision avoidance
const EvolutionTree: React.FC<{ progress: number }> = ({ progress }) => {
  const generations = useMemo(() => {
    const GRID_SIZE = 120; // Finer grid for more detail
    const grid = Array.from({ length: GRID_SIZE }, () => Array(GRID_SIZE).fill(false));
    const generationPaths: string[][] = [];

    let seed = 1337;
    const random = () => {
      const x = Math.sin(seed++) * 10000;
      return x - Math.floor(x);
    };

    const getGridCoords = (x: number, y: number) => ({
      gx: Math.floor(x / 100 * GRID_SIZE),
      gy: Math.floor(y / 100 * GRID_SIZE),
    });

    const checkAndOccupy = (x1: number, y1: number, x2: number, y2: number) => {
      const { gx: gx1, gy: gy1 } = getGridCoords(x1, y1);
      const { gx: gx2, gy: gy2 } = getGridCoords(x2, y2);
      if (gx1 < 0 || gx1 >= GRID_SIZE || gy1 < 0 || gy1 >= GRID_SIZE) return false;
      if (gx2 < 0 || gx2 >= GRID_SIZE || gy2 < 0 || gy2 >= GRID_SIZE) return false;
      const dx = gx2 - gx1, dy = gy2 - gy1;
      const steps = Math.max(Math.abs(dx), Math.abs(dy));
      if (steps === 0) return true;
      const xInc = dx / steps, yInc = dy / steps;
      const cellsToOccupy = [{ gx: gx1, gy: gy1 }];
      let x = gx1 + xInc, y = gy1 + yInc;
      for (let i = 1; i <= steps; i++) {
        const gx = Math.round(x), gy = Math.round(y);
        if (grid[gy][gx]) return false;
        cellsToOccupy.push({ gx, gy });
        x += xInc;
        y += yInc;
      }
      cellsToOccupy.forEach(({ gx, gy }) => { grid[gy][gx] = true; });
      return true;
    };

    let activeBranches = [{ x: 50, y: 99.9, angle: -90 }];

    // Reduced generations and branching probability to improve performance and clarity
    for (let i = 0; i < 22; i++) { 
      if (activeBranches.length === 0) break;
      const newBranches: typeof activeBranches = [];
      const currentGenerationPaths: string[] = [];

      activeBranches.forEach(branch => {
        for (let j = 0; j < 2; j++) {
          // New angle is relative to parent, biased for wider, diagonal growth
          let newAngle = branch.angle + (random() - 0.5) * 120; 
          // Clamp to the upper hemisphere to prevent downward growth
          newAngle = Math.max(-170, Math.min(-10, newAngle));

          const length = random() * 5 + 2; // Slightly longer branches
          const endX = branch.x + length * Math.cos(newAngle * Math.PI / 180);
          const endY = branch.y + length * Math.sin(newAngle * Math.PI / 180);

          if (checkAndOccupy(branch.x, branch.y, endX, endY)) {
            currentGenerationPaths.push(`M ${branch.x} ${branch.y} L ${endX} ${endY}`);
            if (random() > 0.25) {
              newBranches.push({ x: endX, y: endY, angle: newAngle });
            }
          }
        }
      });

      if (currentGenerationPaths.length > 0) {
        generationPaths.push(currentGenerationPaths);
      }
      activeBranches = newBranches;
    }
    return generationPaths;
  }, []);

  const visibleGenerations = Math.floor(generations.length * progress);
  const visibleBranches = generations.slice(0, visibleGenerations).flat();

  return (
    <svg viewBox="0 0 100 100" className="absolute inset-0 w-full h-full overflow-visible">
      {visibleBranches.map((d, i) => (
        <motion.path
          key={i}
          d={d}
          fill="transparent"
          stroke="rgba(255, 255, 255, 0.8)"
          strokeWidth={0.25}
          style={{ filter: 'drop-shadow(0 0 5px rgba(255, 255, 255, 0.9))' }}
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{ duration: 2.5, ease: 'easeOut' }} // Slowed down animation
        />
      ))}
    </svg>
  );
};

const MammalDawnShapes: React.FC<MammalDawnShapesProps> = ({ progress }) => {
  return (
    <div className="absolute inset-0" data-testid="mammal-dawn-container">
      <DriftingParticles />
      <div 
        className="absolute inset-0"
        style={{ transform: 'scale(1.2) translateY(-10%)' }} // Make it bigger and adjust position
      >
        <EvolutionTree progress={progress} />
      </div>
    </div>
  );
};

export default MammalDawnShapes;

