import React from 'react';
import { render, screen } from '@testing-library/react';
import ImpactAndAftermathShapes from './ImpactAndAftermathShapes';

describe('ImpactAndAftermathShapes', () => {
  it('renders the initial impact effects (shockwave and wind) at the beginning of the era', () => {
    render(<ImpactAndAftermathShapes progress={0.1} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // A more robust test would check for the presence of rock and wind streak elements
  });

  it('renders the ash rain during the era', () => {
    render(<ImpactAndAftermathShapes progress={0.2} />);
    const container = screen.getByTestId('impact-container');
    expect(container).toBeInTheDocument();
    // A more robust test would check for the presence of ash particle elements
  });

  it('does not render the initial impact effects later in the era', () => {
    render(<ImpactAndAftermathShapes progress={0.5} />);
    const container = screen.getByTestId('impact-container');
    // The container should still be there for the ash rain
    expect(container).toBeInTheDocument();
    // Here, we'd ideally assert that the rock/wind elements are NOT present.
  });
});
