'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';

interface ImpactAndAftermathShapesProps {
  progress: number;
}

const ImpactAndAftermathShapes: React.FC<ImpactAndAftermathShapesProps> = ({ progress }) => {
  const ashCount = 300;
  const windStreakCount = 25; // Reduced count for performance, increased size for coverage
  const rockCount = 40; // Reduced count for performance

  // --- Memoized Animation Data --- //

  const windStreaks = useMemo(() => {
    return Array.from({ length: windStreakCount }).map((_, i) => ({
      id: `wind-${i}`,
      top: `${Math.random() * 100}%`,
      left: `${Math.random() * -50 - 50}%`, // Start further off-screen
      width: `${Math.random() * 100 + 150}vw`, // Much wider streaks
      height: `${Math.random() * 2 + 1}px`,
      duration: Math.random() * 1.5 + 1, // Slower, more impactful
      delay: Math.random() * 1.5, // Staggered start
    }));
  }, []);

  const rocks = useMemo(() => {
    return Array.from({ length: rockCount }).map((_, i) => ({
      id: `rock-${i}`,
      top: `${Math.random() * 100}%`,
      left: `${Math.random() * 20 - 40}%`, // Start from a wider range on the left
      size: Math.random() * 35 + 10,
      borderRadius: `${Math.random() * 40 + 20}% ${Math.random() * 40 + 20}%`,
      color: `hsl(0, 0%, ${Math.random() * 20 + 10}%)`,
      duration: Math.random() * 1.5 + 0.8,
      delay: Math.random() * 1,
      // Target for the arcing motion
      x: `${Math.random() * 80 + 80}vw`,
      y: `${(Math.random() - 0.5) * 60}vh`,
    }));
  }, []);

  const ash = useMemo(() => {
    return Array.from({ length: ashCount }).map((_, i) => ({
      id: `ash-${i}`,
      x: Math.random() * 100,
      y: -Math.random() * 100 - 10,
      size: Math.random() * 4 + 1,
      duration: Math.random() * 8 + 8,
      delay: Math.random() * 16,
      baseOpacity: Math.random() * 0.5 + 0.2,
    }));
  }, []);

  // --- Visibility and Opacity Control --- //

  const impactIsVisible = progress < 0.7;
  const impactOpacity = progress < 0.7 ? 1 - progress / 0.7 : 0;
  const ashOpacity = progress > 0.15 ? (progress - 0.15) / 0.45 : 0;

  return (
    <div className="absolute inset-0 overflow-hidden" data-testid="impact-container">
      {/* --- High-Performance Impact Animations --- */}
      {impactIsVisible && (
        <motion.div style={{ opacity: impactOpacity }}>
          {/* Wind Streaks */}
          {windStreaks.map((streak) => (
            <motion.div
              key={streak.id}
              className="absolute"
              style={{ top: streak.top, left: streak.left, width: streak.width, height: streak.height, background: 'linear-gradient(to right, rgba(200, 200, 200, 0), rgba(220, 220, 220, 0.3), rgba(200, 200, 200, 0))' }}
              initial={{ x: 0, opacity: 0 }}
              animate={{ x: '200vw', opacity: [0, 1, 1, 0] }}
              transition={{ duration: streak.duration, delay: streak.delay, ease: 'easeIn' }}
            />
          ))}

          {/* Rock Explosions */}
          {rocks.map((rock) => (
            <motion.div
              key={rock.id}
              className="absolute"
              style={{ top: rock.top, left: rock.left, width: rock.size, height: rock.size, backgroundColor: rock.color, borderRadius: rock.borderRadius }}
              initial={{ x: 0, y: 0, opacity: 0, scale: 0.5, rotate: 0 }}
              animate={{ x: rock.x, y: rock.y, opacity: [0, 1, 0], scale: 1, rotate: (Math.random() - 0.5) * 720 }}
              transition={{ duration: rock.duration, delay: rock.delay, ease: 'easeOut' }}
            />
          ))}
        </motion.div>
      )}

      {/* --- High-Performance Ash Rain --- */}
      <motion.div style={{ opacity: ashOpacity }}>
        {ash.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute"
            style={{ left: `${particle.x}%`, top: particle.y, width: particle.size, height: particle.size * 2, backgroundColor: '#333', borderRadius: '50%' }}
            initial={{ y: 0, opacity: 0 }}
            animate={{ y: '110vh', opacity: [0, particle.baseOpacity, 0] }}
            transition={{ duration: particle.duration, delay: particle.delay, repeat: Infinity, repeatType: 'loop', ease: 'linear' }}
          />
        ))}
      </motion.div>
    </div>
  );
};

export default ImpactAndAftermathShapes;
